#!/bin/bash

# Combined Development Script - All Services
# This script runs backend, assistants_web, and coral_web in parallel for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Cohere Toolkit - All Services (Local Development)${NC}"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Error: pyproject.toml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo -e "\n${YELLOW}🛑 Stopping all development servers...${NC}"
    jobs -p | xargs -r kill
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Check if database services are running
echo -e "${BLUE}🔍 Checking database services...${NC}"
if ! nc -z localhost 5432 2>/dev/null; then
    echo -e "${YELLOW}📦 Starting database services...${NC}"
    docker-compose up db redis -d
    echo -e "${BLUE}⏳ Waiting for database to be ready...${NC}"
    sleep 5
fi

# Load nvm if available
if [ -f ~/.nvm/nvm.sh ]; then
    source ~/.nvm/nvm.sh
fi

# Start backend in background
echo -e "${GREEN}🔧 Starting backend server...${NC}"
./scripts/backend-dev.sh &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 10

# Start assistants_web frontend in background
echo -e "${GREEN}🎨 Starting Assistants Web frontend...${NC}"
./scripts/frontend-assistant.sh &
ASSISTANT_PID=$!

# Wait a moment before starting coral_web
sleep 5

# Start coral_web frontend in background
echo -e "${GREEN}🌊 Starting Coral Web frontend...${NC}"
./scripts/frontend-coral.sh &
CORAL_PID=$!

# Display information
echo -e "\n${GREEN}✅ All development servers started!${NC}"
echo -e "${BLUE}   Backend API: http://localhost:8000${NC}"
echo -e "${BLUE}   API Docs: http://localhost:8000/docs${NC}"
echo -e "${BLUE}   Assistants Web (Enterprise): http://localhost:4000${NC}"
echo -e "${BLUE}   Coral Web (Personal): http://localhost:3000${NC}"
echo -e "\n${YELLOW}   Press Ctrl+C to stop all servers${NC}"

# Wait for background processes
wait
