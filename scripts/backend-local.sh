#!/bin/bash

# Backend Local Development Script
# This script runs the FastAPI backend locally with Poetry and hot reload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Cohere Toolkit Backend (Local with Poetry)${NC}"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Error: pyproject.toml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo -e "${RED}❌ Error: Poetry is not installed. Please install Poetry first.${NC}"
    echo -e "${YELLOW}   Install Poetry: https://python-poetry.org/docs/#installation${NC}"
    exit 1
fi

# Load environment variables from .env if it exists
if [ -f ".env" ]; then
    echo -e "${GREEN}📄 Loading environment variables from .env${NC}"
    export $(grep -v '^#' .env | xargs)
fi

# Load environment variables from .env.local if it exists (overrides .env)
if [ -f ".env.local" ]; then
    echo -e "${GREEN}📄 Loading environment variables from .env.local${NC}"
    export $(grep -v '^#' .env.local | xargs)
fi

# Set local database URL for development
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/postgres"
export REDIS_URL="redis://localhost:6379"

# Check if database is running
echo -e "${YELLOW}🔍 Checking database connection...${NC}"
if ! nc -z localhost 5432; then
    echo -e "${RED}❌ Error: PostgreSQL database is not running on localhost:5432${NC}"
    echo -e "${YELLOW}   Start the database with: docker-compose up db -d${NC}"
    exit 1
fi

# Check if Redis is running
if ! nc -z localhost 6379; then
    echo -e "${RED}❌ Error: Redis is not running on localhost:6379${NC}"
    echo -e "${YELLOW}   Start Redis with: docker-compose up redis -d${NC}"
    exit 1
fi

# Install dependencies if needed
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
poetry install --with dev

# Run database migrations
echo -e "${YELLOW}🔄 Running database migrations...${NC}"
poetry run alembic -c src/backend/alembic.ini upgrade head

# Start the backend server
echo -e "${GREEN}✅ Starting backend server with hot reload...${NC}"
echo -e "${BLUE}   Backend will be available at: http://localhost:8000${NC}"
echo -e "${BLUE}   API docs will be available at: http://localhost:8000/docs${NC}"
echo -e "${YELLOW}   Press Ctrl+C to stop the server${NC}"

# Start backend with hot reload
cd src/backend
poetry run uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
