#!/bin/bash

# Frontend Development Script - Coral Web
# This script runs the Coral Web frontend locally with hot reload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Coral Web Frontend (Local Development)${NC}"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Error: pyproject.toml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Choose which frontend to run
FRONTEND_DIR="src/interfaces/coral_web"
FRONTEND_NAME="Coral Web"

# Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
    echo -e "${RED}❌ Error: Frontend directory $FRONTEND_DIR not found.${NC}"
    exit 1
fi

# Change to frontend directory
cd "$FRONTEND_DIR"

# Load nvm if available
if [ -f ~/.nvm/nvm.sh ]; then
    source ~/.nvm/nvm.sh
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Error: Node.js is not installed. Please install Node.js first.${NC}"
    echo -e "${BLUE}   Visit: https://nodejs.org/ or use nvm: nvm install 20${NC}"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ Error: npm is not installed. Please install npm first.${NC}"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
    npm install
fi

# Check if backend is running
echo -e "${BLUE}🔍 Checking backend connection...${NC}"
if ! nc -z localhost 8000 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Warning: Backend is not running on localhost:8000${NC}"
    echo -e "${BLUE}   Please start the backend with: ./scripts/backend-dev.sh${NC}"
    echo -e "${BLUE}   Or run both with: ./scripts/dev.sh${NC}"
fi

# Set environment variables for frontend
export NEXT_PUBLIC_API_HOSTNAME=http://localhost:8000

# Start the frontend server
echo -e "${GREEN}✅ Starting $FRONTEND_NAME with hot reload...${NC}"
echo -e "${BLUE}   Frontend will be available at: http://localhost:3000${NC}"
echo -e "${YELLOW}   Press Ctrl+C to stop the server${NC}"

# Start the development server on port 3000
npm run dev -- --port 3000
