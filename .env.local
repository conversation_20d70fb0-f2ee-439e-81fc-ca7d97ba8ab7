# Local Development Environment Variables
# Copy this file to .env and update the values as needed

# Cohere API Configuration
COHERE_API_KEY=maBjlY9QEGerJDQYRcLWu1FM2dYtAbSKBbUISBhj

# Database Configuration (using local Docker containers)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres

# Redis Configuration (using local Docker container)
REDIS_URL=redis://localhost:6379

# Backend Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000

# Frontend Configuration
FRONTEND_HOST=localhost
FRONTEND_PORT=4000

# CORS Configuration for local development
ALLOWED_ORIGINS=http://localhost:4000,http://127.0.0.1:4000

# Python Interpreter Tool (optional - for code execution)
PYTHON_INTERPRETER_URL=http://localhost:8080

# Development Mode
ENVIRONMENT=development
DEBUG=true

# Optional: Tool configurations (can be left empty for basic functionality)
# GOOGLE_DRIVE_CLIENT_ID=
# GOOGLE_DRIVE_CLIENT_SECRET=
# SLACK_CLIENT_ID=
# SLACK_CLIENT_SECRET=
# GMAIL_CLIENT_ID=
# GMAIL_CLIENT_SECRET=
# GITHUB_CLIENT_ID=
# GITHUB_CLIENT_SECRET=
# SHAREPOINT_TENANT_ID=
# SHAREPOINT_CLIENT_ID=
# SHAREPOINT_CLIENT_SECRET=

# Session Secret (for authentication)
SESSION_SECRET=your-session-secret-here

# Auth Configuration (optional)
# AUTH_STRATEGY=basic
# NEXT_PUBLIC_API_HOSTNAME=http://localhost:8000
