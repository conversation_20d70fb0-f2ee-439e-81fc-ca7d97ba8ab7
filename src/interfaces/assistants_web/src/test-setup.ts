import { vi } from 'vitest';

// Mock environment variables
Object.defineProperty(window, '__ENV', {
  value: {
    NEXT_PUBLIC_API_HOSTNAME: 'http://localhost:8000',
    NEXT_PUBLIC_FRONTEND_HOSTNAME: 'http://localhost:4000',
  },
  writable: true,
});

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useParams: () => ({}),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
  QueryClient: vi.fn(),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Zustand stores
const mockStoreState = {
  currentOrganization: null,
  organizations: [],
  isLoading: false,
  error: null,
  setCurrentOrganization: vi.fn(),
  setOrganizations: vi.fn(),
  setLoading: vi.fn(),
  setError: vi.fn(),
  resetOrganizationState: vi.fn(),
};

const mockUseOrganizationStore = vi.fn(() => mockStoreState) as any;
mockUseOrganizationStore.getState = vi.fn(() => mockStoreState);

vi.mock('@/stores', () => ({
  useOrganizationStore: mockUseOrganizationStore,
}));

// Mock next-client-cookies
vi.mock('next-client-cookies', () => ({
  useCookies: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn(),
  })),
}));

vi.mock('next-client-cookies/server', () => ({
  CookiesProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Note: use-organization hooks are not mocked globally to allow individual tests to test them properly
