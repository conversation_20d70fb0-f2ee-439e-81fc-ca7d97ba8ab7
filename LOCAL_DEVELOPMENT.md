# Local Development Setup

This guide shows you how to run the Cohere Toolkit locally with hot reload for faster development. The backend runs locally with Poetry, while database services run in Docker.

## 🚀 Quick Start

### Prerequisites

- **Python 3.11** with [Poetry](https://python-poetry.org/docs/#installation)
- **Node.js 20.12.2+** with npm
- **Docker** (for database services only)

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd cohere-toolkit

# Install all dependencies (backend + both frontends)
npm run setup
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.local .env

# Edit .env and update your Cohere API key
# COHERE_API_KEY=your-actual-api-key-here
```

### 3. Start Development

```bash
# Option 1: Start backend + assistants_web (recommended)
npm run dev

# Option 2: Start all services (backend + both frontends)
npm run dev:all

# Option 3: Start services individually
npm run db:start              # Start databases only
npm run backend:dev           # Start backend only
npm run frontend:assistant    # Start assistants_web only (port 4000)
npm run frontend:coral        # Start coral_web only (port 3000)
```

### 4. Access the Applications

- **Assistants Web (Enterprise)**: http://localhost:4000
- **Coral Web (Personal)**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📁 Project Structure

```
cohere-toolkit/
├── scripts/
│   ├── dev.sh                 # Backend + assistants_web
│   ├── dev-all.sh             # Backend + both frontends
│   ├── backend-dev.sh         # Backend only (local with Poetry)
│   ├── frontend-assistant.sh  # Assistants Web frontend (port 4000)
│   └── frontend-coral.sh      # Coral Web frontend (port 3000)
├── src/
│   ├── backend/            # FastAPI backend
│   └── interfaces/
│       ├── coral_web/      # Next.js frontend (personal)
│       └── assistants_web/ # Next.js frontend (enterprise)
├── .env.local              # Environment variables template
├── package.json            # Development scripts
└── LOCAL_DEVELOPMENT.md    # This file
```

## 🛠 Available Scripts

### NPM Scripts

```bash
# Development
npm run dev                  # Start backend + assistants_web (recommended)
npm run dev:all              # Start backend + both frontends
npm run backend:dev          # Start backend only with hot reload
npm run frontend:dev         # Start assistants_web frontend (alias)
npm run frontend:assistant   # Start assistants_web frontend (port 4000)
npm run frontend:coral       # Start coral_web frontend (port 3000)

# Database
npm run db:start             # Start database services (PostgreSQL + Redis)
npm run db:stop              # Stop database services
npm run db:logs              # View database logs

# Setup & Maintenance
npm run setup                # Install all dependencies (backend + frontends)
npm run setup:backend        # Install backend dependencies only
npm run setup:frontend       # Install frontend dependencies only
npm run migrate              # Run database migrations

# Testing & Quality
npm run test                 # Run backend tests
npm run lint                 # Check code style
npm run lint:fix             # Fix code style issues
npm run format               # Format code
```

### Direct Script Usage

```bash
./scripts/dev.sh                # Backend + assistants_web
./scripts/dev-all.sh            # Backend + both frontends
./scripts/backend-dev.sh        # Backend only
./scripts/frontend-assistant.sh # Assistants_web frontend (port 4000)
./scripts/frontend-coral.sh     # Coral_web frontend (port 3000)
```

## 🔧 Development Workflow

### Hot Reload

- **Backend**: Runs locally with Poetry using `uvicorn --reload` for automatic restart on file changes
- **Frontend**: Uses Next.js dev server with hot module replacement
- **Database**: Runs in Docker containers for consistency

### Database Management

```bash
# Start databases (PostgreSQL + Redis)
npm run db:start

# Run migrations
npm run migrate

# View database logs
npm run db:logs

# Stop databases
npm run db:stop
```

### Environment Variables

The `.env` or `.env.local` file contains all necessary environment variables:

```bash
# Required
COHERE_API_KEY=your-api-key-here

# Database (automatically configured for local Docker)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres
REDIS_URL=redis://localhost:6379

# Development settings
ENVIRONMENT=development
DEBUG=true
ALLOWED_ORIGINS=http://localhost:4000,http://localhost:3000,http://127.0.0.1:4000,http://127.0.0.1:3000

# Authentication (if enabled)
AUTH_STRATEGY=Basic
```

## 🐛 Troubleshooting

### Backend Issues

```bash
# Check if Python 3.11 is installed
python3.11 --version

# Check if Poetry is installed
poetry --version

# Install backend dependencies
poetry install --with dev

# Check if database is running
nc -z localhost 5432

# Check backend logs
# Backend logs appear in the terminal where you ran the script
```

### Frontend Issues

```bash
# Check if Node.js is installed
node --version

# Install frontend dependencies
cd src/interfaces/assistants_web && npm install
cd src/interfaces/coral_web && npm install

# Check if backend is running
curl http://localhost:8000/health

# Clear Next.js cache
rm -rf src/interfaces/assistants_web/.next
rm -rf src/interfaces/coral_web/.next
```

### Database Issues

```bash
# Restart database services
npm run db:stop && npm run db:start

# Check database logs
npm run db:logs

# Reset database (WARNING: This will delete all data)
docker-compose down -v && npm run db:start
```

## 🔄 Development Modes

### Single Frontend Development (Recommended)

```bash
# Backend + Assistants Web (Enterprise)
npm run dev

# Backend + Coral Web (Personal)
npm run backend:dev &
npm run frontend:coral
```

### Multi-Frontend Development

```bash
# Backend + Both Frontends
npm run dev:all
```

### Individual Services

```bash
# Start databases only
npm run db:start

# Start backend only
npm run backend:dev

# Start specific frontend only
npm run frontend:assistant  # Port 4000
npm run frontend:coral      # Port 3000
```

## 📝 Notes

- **Database**: Runs in Docker containers for consistency and easy setup
- **Backend**: Runs locally with Poetry for faster development and easier debugging
- **Frontend**: Both frontends run locally with npm for hot module replacement
- **Hot Reload**: All services automatically reload on file changes
- **API Key**: Make sure to set your actual Cohere API key in `.env` or `.env.local`
- **Ports**:
  - Backend: 8000
  - Assistants Web (Enterprise): 4000
  - Coral Web (Personal): 3000
  - PostgreSQL: 5432
  - Redis: 6379
- **Authentication**: Basic authentication is enabled by default in the configuration

## 🆘 Getting Help

If you encounter issues:

1. Check that all prerequisites are installed
2. Verify your `.env` file has the correct API key
3. Ensure database services are running: `npm run db:start`
4. Check the troubleshooting section above
5. Look at the logs in the terminal where you started the services
